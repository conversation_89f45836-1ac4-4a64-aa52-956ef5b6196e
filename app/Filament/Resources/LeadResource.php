<?php

namespace App\Filament\Resources;

use App\Filament\Resources\LeadResource\Pages;
use App\Models\Lead;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

// For global search on relationship

class LeadResource extends Resource
{
    protected static ?string $model = Lead::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Toggle::make('website_status')
                    ->label('Website Status')
                    ->default(false),
                Forms\Components\Toggle::make('website_ssl')
                    ->label('SSL Enabled'),
                Forms\Components\TextInput::make('website_url')
                    ->label('Website URL')
                    ->url()
                    ->maxLength(255),
                Forms\Components\Textarea::make('website_technologies')
                    ->label('Website Technologies')
                    ->helperText('Enter technologies, comma-separated or one per line.'),
                Forms\Components\TextInput::make('website_platform_name')
                    ->label('Platform Name')
                    ->maxLength(255),
                Forms\Components\TextInput::make('website_platform_version')
                    ->label('Platform Version')
                    ->maxLength(255),
                Forms\Components\TextInput::make('website_theme_name')
                    ->label('Theme Name')
                    ->maxLength(255),
                Forms\Components\TextInput::make('website_theme_version')
                    ->label('Theme Version')
                    ->maxLength(255),
                Forms\Components\Toggle::make('website_type')
                    ->label('Website Type (e.g., E-commerce)'),
                Forms\Components\TextInput::make('domain')
                    ->label('Domain')
                    ->maxLength(255),
                Forms\Components\TextInput::make('tld')
                    ->label('TLD')
                    ->maxLength(255),
                Forms\Components\Toggle::make('domain_status')
                    ->label('Domain Status')
                    ->default(false),
                Forms\Components\DatePicker::make('domain_created')
                    ->label('Domain Created'),
                Forms\Components\DatePicker::make('domain_expires')
                    ->label('Domain Expires'),
                Forms\Components\TextInput::make('domain_owner')
                    ->label('Domain Owner')
                    ->maxLength(255),
                Forms\Components\TextInput::make('company_number')
                    ->label('Company Number')
                    ->maxLength(255),
                Forms\Components\Textarea::make('comment')
                    ->label('Comment')
                    ->columnSpanFull(),
                Forms\Components\Section::make('Website Information')
                    ->columns(2)
                    ->schema([
                        Forms\Components\TextInput::make('website_powered_by')
                            ->label('Powered By')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('website_hreflang')
                            ->label('Hreflang Tags')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('website_phone')
                            ->label('Website Phone')
                            ->tel()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('website_email')
                            ->label('Website Email')
                            ->email()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('website_vat')
                            ->label('Website VAT')
                            ->maxLength(255),
                    ]),
                Forms\Components\Section::make('Google PageSpeed Scores')
                    ->columns(2)
                    ->schema([
                        Forms\Components\Fieldset::make('Desktop')
                            ->schema([
                                Forms\Components\TextInput::make('google_pagespeed_desktop_performance_score')
                                    ->label('Performance')
                                    ->numeric()->minValue(0)->maxValue(100),
                                Forms\Components\TextInput::make('google_pagespeed_desktop_accessibility_score')
                                    ->label('Accessibility')
                                    ->numeric()->minValue(0)->maxValue(100),
                                Forms\Components\TextInput::make('google_pagespeed_desktop_seo_score')
                                    ->label('SEO')
                                    ->numeric()->minValue(0)->maxValue(100),
                                Forms\Components\TextInput::make('google_pagespeed_desktop_best_practices_score')
                                    ->label('Best Practices')
                                    ->numeric()->minValue(0)->maxValue(100),
                            ]),
                        Forms\Components\Fieldset::make('Mobile')
                            ->schema([
                                Forms\Components\TextInput::make('google_pagespeed_mobile_performance_score')
                                    ->label('Performance')
                                    ->numeric()->minValue(0)->maxValue(100),
                                Forms\Components\TextInput::make('google_pagespeed_mobile_accessibility_score')
                                    ->label('Accessibility')
                                    ->numeric()->minValue(0)->maxValue(100),
                                Forms\Components\TextInput::make('google_pagespeed_mobile_seo_score')
                                    ->label('SEO')
                                    ->numeric()->minValue(0)->maxValue(100),
                                Forms\Components\TextInput::make('google_pagespeed_mobile_best_practices_score')
                                    ->label('Best Practices')
                                    ->numeric()->minValue(0)->maxValue(100),
                            ]),
                    ]),
                Forms\Components\Section::make('PageSpeed Screenshots')
                    ->columns(2)
                    ->schema([
                        Forms\Components\Fieldset::make('Desktop Screenshot')
                            ->schema([
                                Forms\Components\Textarea::make('google_pagespeed_desktop_screenshot')
                                    ->label('Desktop Screenshot URL')
                                    ->rows(3)
                                    ->columnSpanFull(),
                                Forms\Components\ViewField::make('desktop_screenshot_preview')
                                    ->label('Preview')
                                    ->view('filament.components.screenshot-preview')
                                    ->viewData([
                                        'field_name' => 'google_pagespeed_desktop_screenshot',
                                        'alt' => 'Desktop Screenshot'
                                    ])
                                    ->columnSpanFull()
                                    ->visible(fn ($record) => !empty($record?->google_pagespeed_desktop_screenshot)),
                            ]),
                        Forms\Components\Fieldset::make('Mobile Screenshot')
                            ->schema([
                                Forms\Components\Textarea::make('google_pagespeed_mobile_screenshot')
                                    ->label('Mobile Screenshot URL')
                                    ->rows(3)
                                    ->columnSpanFull(),
                                Forms\Components\ViewField::make('mobile_screenshot_preview')
                                    ->label('Preview')
                                    ->view('filament.components.screenshot-preview')
                                    ->viewData([
                                        'field_name' => 'google_pagespeed_mobile_screenshot',
                                        'alt' => 'Mobile Screenshot'
                                    ])
                                    ->columnSpanFull()
                                    ->visible(fn ($record) => !empty($record?->google_pagespeed_mobile_screenshot)),
                            ]),
                    ]),
                Forms\Components\Toggle::make('contact')
                    ->label('Contact Made'),
                Forms\Components\Toggle::make('relevant')
                    ->label('Relevant Lead'),
                Forms\Components\Toggle::make('manual')
                    ->label('Manual Entry'),
                Forms\Components\Toggle::make('deleted')
                    ->label('Marked as Deleted'),
                Forms\Components\DateTimePicker::make('data_updated')
                    ->label('Data Last Updated'),
                Forms\Components\DateTimePicker::make('change')
                    ->label('Last Significant Change Date'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\IconColumn::make('website_status')
                    ->label('Website Status')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\IconColumn::make('website_ssl')
                    ->label('Website SSL')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('website_url')
                    ->label('Website URL')
                    ->searchable()
                    ->sortable()
                    ->limit(30)
                    ->tooltip(fn (Lead $record): string => $record->website_url ?? ''),
                Tables\Columns\TextColumn::make('website_platform_name')
                    ->label('Platform Name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('website_platform_version')
                    ->label('Platform Version')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('website_theme_name')
                    ->label('Theme Name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('website_theme_version')
                    ->label('Theme Version')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('website_technologies')
                    ->label('Website Technologies')
                    ->sortable()
                    ->formatStateUsing(fn ($state) => is_array($state) ? implode(', ', $state) : $state)
                    ->limit(20)
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->tooltip(fn (Lead $record): string => is_array($record->website_technologies) ? implode(', ', $record->website_technologies) : (string) ($record->website_technologies ?? '')),
                Tables\Columns\IconColumn::make('website_type')
                    ->label('Website Type')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('domain')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('tld')
                    ->label('TLD')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\IconColumn::make('domain_status')
                    ->label('Domain Status')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('domain_created')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('domain_expires')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('domain_owner')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('website_powered_by')
                    ->label('Powered By')
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('website_hreflang')
                    ->label('Hreflang')
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('website_phone')
                    ->label('Website Phone')
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('website_email')
                    ->label('Website Email')
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('website_vat')
                    ->label('Website VAT')
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('google_pagespeed_desktop_performance_score')
                    ->label('Desktop Performance Score')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('google_pagespeed_desktop_accessibility_score')
                    ->label('Desktop Accessibility Score')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('google_pagespeed_desktop_seo_score')
                    ->label('Desktop SEO Score')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('google_pagespeed_desktop_best_practices_score')
                    ->label('Desktop Best Practices Score')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('google_pagespeed_mobile_performance_score')
                    ->label('Mobile Performance Score')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('google_pagespeed_mobile_accessibility_score')
                    ->label('Mobile Accessibility Score')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('google_pagespeed_mobile_seo_score')
                    ->label('Mobile SEO Score')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('google_pagespeed_mobile_best_practices_score')
                    ->label('Mobile Best Practices Score')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\ViewColumn::make('google_pagespeed_desktop_screenshot')
                    ->label('Desktop Screenshot')
                    ->view('filament.components.screenshot-thumbnail')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\ViewColumn::make('google_pagespeed_mobile_screenshot')
                    ->label('Mobile Screenshot')
                    ->view('filament.components.screenshot-thumbnail')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\IconColumn::make('contact')
                    ->label('Contact Made')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\IconColumn::make('relevant')
                    ->label('Relevant Lead')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\IconColumn::make('manual')
                    ->label('Manual Entry')
                    ->boolean()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\IconColumn::make('deleted')
                    ->label('Marked as Deleted')
                    ->boolean()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('data_updated')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('change')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->date()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->date()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                // You can add filters here, for example:
                // Tables\Filters\TernaryFilter::make('website_ssl'),
                // Tables\Filters\TernaryFilter::make('contact'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('updated_at', 'desc')
            ->defaultPaginationPageOption(20)
            ->paginationPageOptions([20, 50, 100, 500, 1000]);
    }

    public static function getGlobalSearchEloquentQuery(): Builder
    {
        return parent::getGlobalSearchEloquentQuery()->with(['company']);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLeads::route('/'),
            'create' => Pages\CreateLead::route('/create'),
            'edit' => Pages\EditLead::route('/{record}/edit'),
        ];
    }
}
