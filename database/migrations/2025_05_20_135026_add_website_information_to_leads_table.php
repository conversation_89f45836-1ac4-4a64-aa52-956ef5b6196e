<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            $table->string('website_powered_by')->nullable()->after('website_status');
            $table->string('website_hreflang')->nullable()->after('website_powered_by');
            $table->string('website_phone')->nullable()->after('website_hreflang');
            $table->string('website_email')->nullable()->after('website_phone');
            $table->string('website_vat')->nullable()->after('website_email');
            $table->string('website_title', 255)->nullable()->after('website_vat');
            $table->text('website_meta_description')->nullable()->after('website_title');
            $table->ipAddress('website_ip')->nullable()->after('website_meta_description');
            $table->string('website_made_by')->nullable()->after('website_ip');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('leads', function (Blueprint $table) {
            $table->dropColumn([
                'website_powered_by',
                'website_hreflang',
                'website_phone',
                'website_email',
                'website_vat',
                'website_title',
                'website_meta_description',
                'website_ip',
                'website_made_by',
            ]);
        });
    }
};
